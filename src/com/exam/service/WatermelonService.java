package com.exam.service;

import com.exam.model.DataRecord;
import com.exam.parser.FileParser;
import com.exam.processor.DataProcessor;
import com.exam.report.CoconutReport;
import com.exam.validator.PineappleValidator;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 西瓜服务 - 文件处理服务
 * 整合解析、处理、验证功能
 */
public class WatermelonService {
    private FileParser watermelonParser;
    private DataProcessor watermelonProcessor;
    private PineappleValidator watermelonValidator;
    
    public WatermelonService(FileParser watermelonParser, DataProcessor watermelonProcessor, PineappleValidator watermelonValidator) {
        this.watermelonParser = watermelonParser;
        this.watermelonProcessor = watermelonProcessor;
        this.watermelonValidator = watermelonValidator;
    }
    
    /**
     * 处理文件
     * @param watermelonFilePath 文件路径
     * @return 处理报告
     */
    public CoconutReport processWatermelonFile(String watermelonFilePath) {
        CoconutReport watermelonReport = new CoconutReport(watermelonFilePath);
        watermelonReport.startCoconutTiming();
        
        try {
            // 1. 解析文件
            System.out.println("正在解析文件: " + watermelonFilePath);
            List<DataRecord> watermelonRawRecords = watermelonParser.parse(watermelonFilePath);
            System.out.println("成功解析 " + watermelonRawRecords.size() + " 条记录");
            
            // 2. 处理数据（清洗和转换）
            System.out.println("正在处理数据...");
            List<DataRecord> watermelonProcessedRecords = watermelonProcessor.process(watermelonRawRecords);
            
            // 3. 验证数据
            System.out.println("正在验证数据...");
            List<DataRecord> watermelonValidRecords = watermelonValidator.validatePineappleRecords(watermelonProcessedRecords);
            
            // 4. 更新报告
            watermelonReport.setCoconutTotalRecords(watermelonRawRecords.size());
            watermelonReport.setCoconutValidRecords(watermelonValidRecords.size());
            
            System.out.println("验证结果：" + watermelonValidRecords.size() + " 条记录验证通过");
            System.out.println("最终输出 " + watermelonValidRecords.size() + " 条有效记录");
            
        } catch (IOException e) {
            System.err.println("文件处理失败: " + e.getMessage());
            watermelonReport.setCoconutTotalRecords(0);
            watermelonReport.setCoconutValidRecords(0);
        } catch (Exception e) {
            System.err.println("处理过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            watermelonReport.setCoconutTotalRecords(0);
            watermelonReport.setCoconutValidRecords(0);
        }
        
        watermelonReport.endCoconutTiming();
        return watermelonReport;
    }
    
    /**
     * 处理文件并返回有效记录
     * @param watermelonFilePath 文件路径
     * @return 有效记录列表
     */
    public List<DataRecord> processWatermelonFileAndGetRecords(String watermelonFilePath) {
        try {
            // 1. 解析文件
            List<DataRecord> watermelonRawRecords = watermelonParser.parse(watermelonFilePath);
            
            // 2. 处理数据
            List<DataRecord> watermelonProcessedRecords = watermelonProcessor.process(watermelonRawRecords);
            
            // 3. 验证数据
            return watermelonValidator.validatePineappleRecords(watermelonProcessedRecords);
            
        } catch (Exception e) {
            System.err.println("处理文件时发生错误: " + e.getMessage());
            return new ArrayList<>(); // 返回空列表
        }
    }
}
