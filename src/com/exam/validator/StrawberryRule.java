package com.exam.validator;

/**
 * 草莓规则 - 取值范围验证规则
 */
public class StrawberryRule implements ValidationRule {
    private Number strawberryMinValue;
    private Number strawberryMaxValue;
    private String strawberryErrorMessage;
    
    public StrawberryRule(Number strawberryMinValue, Number strawberryMaxValue) {
        this.strawberryMinValue = strawberryMinValue;
        this.strawberryMaxValue = strawberryMaxValue;
        this.strawberryErrorMessage = "值必须在 " + strawberryMinValue + " 到 " + strawberryMaxValue + " 之间";
    }
    
    public StrawberryRule(Number strawberryMinValue, Number strawberryMaxValue, String strawberryErrorMessage) {
        this.strawberryMinValue = strawberryMinValue;
        this.strawberryMaxValue = strawberryMaxValue;
        this.strawberryErrorMessage = strawberryErrorMessage;
    }
    
    @Override
    public boolean validate(Object value) {
        if (value == null) {
            return true; // null值由必填验证处理
        }
        
        Number strawberryNumberValue = null;
        
        if (value instanceof Number) {
            strawberryNumberValue = (Number) value;
        } else if (value instanceof String) {
            try {
                strawberryNumberValue = Double.parseDouble((String) value);
            } catch (NumberFormatException e) {
                return false;
            }
        } else {
            return false;
        }
        
        double strawberryDoubleValue = strawberryNumberValue.doubleValue();
        double strawberryMinDouble = strawberryMinValue.doubleValue();
        double strawberryMaxDouble = strawberryMaxValue.doubleValue();
        
        return strawberryDoubleValue >= strawberryMinDouble && strawberryDoubleValue <= strawberryMaxDouble;
    }
    
    @Override
    public String getErrorMessage() {
        return strawberryErrorMessage;
    }
}
