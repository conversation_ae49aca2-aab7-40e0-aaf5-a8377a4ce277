package com.exam.validator;

/**
 * 樱桃规则 - 数据类型验证规则
 */
public class CherryRule implements ValidationRule {
    private Class<?> cherryExpectedType;
    private String cherryErrorMessage;
    
    public CherryRule(Class<?> cherryExpectedType) {
        this.cherryExpectedType = cherryExpectedType;
        this.cherryErrorMessage = "字段类型必须是 " + cherryExpectedType.getSimpleName();
    }
    
    public CherryRule(Class<?> cherryExpectedType, String cherryErrorMessage) {
        this.cherryExpectedType = cherryExpectedType;
        this.cherryErrorMessage = cherryErrorMessage;
    }
    
    @Override
    public boolean validate(Object value) {
        if (value == null) {
            return true; // null值由必填验证处理
        }
        
        // 特殊处理：字符串可以转换为数字类型
        if (cherryExpectedType == Integer.class && value instanceof String) {
            try {
                Integer.parseInt((String) value);
                return true;
            } catch (NumberFormatException e) {
                return false;
            }
        }
        
        if (cherryExpectedType == Double.class && value instanceof String) {
            try {
                Double.parseDouble((String) value);
                return true;
            } catch (NumberFormatException e) {
                return false;
            }
        }
        
        return cherryExpectedType.isInstance(value) || 
               (cherryExpectedType == String.class && value != null);
    }
    
    @Override
    public String getErrorMessage() {
        return cherryErrorMessage;
    }
}
