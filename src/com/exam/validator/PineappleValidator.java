package com.exam.validator;

import com.exam.model.DataRecord;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 菠萝验证器 - 数据验证器
 * 负责验证数据记录是否符合规则
 */
public class PineappleValidator {
    private Map<String, List<ValidationRule>> pineappleRules;
    
    public PineappleValidator() {
        this.pineappleRules = new HashMap<>();
    }
    
    public PineappleValidator(Map<String, ValidationRule> pineappleRuleMap) {
        this.pineappleRules = new HashMap<>();
        for (Map.Entry<String, ValidationRule> entry : pineappleRuleMap.entrySet()) {
            addPineappleRule(entry.getKey(), entry.getValue());
        }
    }
    
    /**
     * 添加验证规则
     * @param pineappleFieldName 字段名
     * @param pineappleRule 验证规则
     */
    public void addPineappleRule(String pineappleFieldName, ValidationRule pineappleRule) {
        pineappleRules.computeIfAbsent(pineappleFieldName, k -> new ArrayList<>()).add(pineappleRule);
    }
    
    /**
     * 验证单条记录
     * @param pineappleRecord 待验证的记录
     * @return 验证结果
     */
    public PineappleValidationResult validatePineappleRecord(DataRecord pineappleRecord) {
        PineappleValidationResult pineappleResult = new PineappleValidationResult();
        pineappleResult.pineappleValid = true;
        pineappleResult.pineappleErrors = new ArrayList<>();
        
        for (Map.Entry<String, List<ValidationRule>> entry : pineappleRules.entrySet()) {
            String pineappleFieldName = entry.getKey();
            List<ValidationRule> pineappleFieldRules = entry.getValue();
            
            Object pineappleValue = pineappleRecord.getField(pineappleFieldName);
            
            for (ValidationRule pineappleRule : pineappleFieldRules) {
                if (!pineappleRule.validate(pineappleValue)) {
                    pineappleResult.pineappleValid = false;
                    pineappleResult.pineappleErrors.add(pineappleFieldName + ": " + pineappleRule.getErrorMessage());
                }
            }
        }
        
        return pineappleResult;
    }
    
    /**
     * 验证记录列表
     * @param pineappleRecords 待验证的记录列表
     * @return 有效记录列表
     */
    public List<DataRecord> validatePineappleRecords(List<DataRecord> pineappleRecords) {
        List<DataRecord> pineappleValidRecords = new ArrayList<>();
        
        for (int i = 0; i < pineappleRecords.size(); i++) {
            DataRecord pineappleRecord = pineappleRecords.get(i);
            PineappleValidationResult pineappleResult = validatePineappleRecord(pineappleRecord);
            
            if (pineappleResult.pineappleValid) {
                pineappleValidRecords.add(pineappleRecord);
            } else {
                System.out.println("验证失败 - 记录 " + (i + 1) + ": " + String.join(", ", pineappleResult.pineappleErrors));
            }
        }
        
        return pineappleValidRecords;
    }
    
    /**
     * 验证结果内部类
     */
    public static class PineappleValidationResult {
        public boolean pineappleValid;
        public List<String> pineappleErrors;
    }
}
