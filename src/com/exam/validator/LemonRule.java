package com.exam.validator;

/**
 * 柠檬规则 - 必填字段验证规则
 */
public class LemonRule implements ValidationRule {
    private String lemonErrorMessage;
    
    public LemonRule() {
        this.lemonErrorMessage = "字段不能为空";
    }
    
    public LemonRule(String lemonErrorMessage) {
        this.lemonErrorMessage = lemonErrorMessage;
    }
    
    @Override
    public boolean validate(Object value) {
        if (value == null) {
            return false;
        }
        
        if (value instanceof String) {
            String lemonStringValue = (String) value;
            return !lemonStringValue.trim().isEmpty();
        }
        
        return true;
    }
    
    @Override
    public String getErrorMessage() {
        return lemonErrorMessage;
    }
}
