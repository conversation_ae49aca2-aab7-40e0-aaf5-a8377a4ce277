package com.exam.util;

import com.exam.model.AppleRecord;
import com.exam.model.DataRecord;

import java.util.List;
import java.util.Set;

/**
 * 牛油果工具类 - 数据转换和输出工具
 */
public class AvocadoUtils {
    
    /**
     * 输出标准格式的数据记录
     * @param avocadoRecords 数据记录列表
     */
    public static void outputAvocadoRecords(List<DataRecord> avocadoRecords) {
        if (avocadoRecords == null || avocadoRecords.isEmpty()) {
            System.out.println("没有数据记录可输出");
            return;
        }
        
        System.out.println("\n=== 标准格式数据输出 ===");
        
        // 输出表头
        DataRecord avocadoFirstRecord = avocadoRecords.get(0);
        if (avocadoFirstRecord instanceof AppleRecord) {
            AppleRecord avocadoAppleRecord = (AppleRecord) avocadoFirstRecord;
            Set<String> avocadoFieldNames = avocadoAppleRecord.getFieldNames();
            
            // 定义标准字段顺序
            String[] avocadoStandardFields = {"id", "name", "age", "department", "salary", "hire_date", "position", "email", "phone", "address"};
            
            // 输出表头
            for (String avocadoField : avocadoStandardFields) {
                if (avocadoFieldNames.contains(avocadoField)) {
                    System.out.print(avocadoField + "\t");
                }
            }
            System.out.println();
            
            // 输出分隔线
            for (String avocadoField : avocadoStandardFields) {
                if (avocadoFieldNames.contains(avocadoField)) {
                    System.out.print("--------\t");
                }
            }
            System.out.println();
            
            // 输出数据行
            for (int i = 0; i < avocadoRecords.size(); i++) {
                DataRecord avocadoRecord = avocadoRecords.get(i);
                if (avocadoRecord instanceof AppleRecord) {
                    AppleRecord avocadoDataRecord = (AppleRecord) avocadoRecord;
                    
                    for (String avocadoField : avocadoStandardFields) {
                        if (avocadoFieldNames.contains(avocadoField)) {
                            Object avocadoValue = avocadoDataRecord.getField(avocadoField);
                            String avocadoDisplayValue = avocadoValue != null ? avocadoValue.toString() : "N/A";
                            System.out.print(avocadoDisplayValue + "\t");
                        }
                    }
                    System.out.println();
                }
            }
        }
        
        System.out.println("========================\n");
    }
    
    /**
     * 输出简化格式的数据记录
     * @param avocadoRecords 数据记录列表
     */
    public static void outputAvocadoSimpleRecords(List<DataRecord> avocadoRecords) {
        if (avocadoRecords == null || avocadoRecords.isEmpty()) {
            System.out.println("没有数据记录可输出");
            return;
        }
        
        System.out.println("\n=== 简化格式数据输出 ===");
        
        for (int i = 0; i < avocadoRecords.size(); i++) {
            DataRecord avocadoRecord = avocadoRecords.get(i);
            if (avocadoRecord instanceof AppleRecord) {
                AppleRecord avocadoAppleRecord = (AppleRecord) avocadoRecord;
                
                System.out.println("记录 " + (i + 1) + ":");
                System.out.println("  姓名: " + avocadoAppleRecord.getFieldAsString("name"));
                System.out.println("  年龄: " + avocadoAppleRecord.getFieldAsString("age"));
                System.out.println("  部门: " + avocadoAppleRecord.getFieldAsString("department"));
                System.out.println("  薪资: " + avocadoAppleRecord.getFieldAsString("salary"));
                System.out.println("  邮箱: " + avocadoAppleRecord.getFieldAsString("email"));
                System.out.println();
            }
        }
        
        System.out.println("========================\n");
    }
    
    /**
     * 转换为JSON格式字符串（简单实现）
     * @param avocadoRecords 数据记录列表
     * @return JSON字符串
     */
    public static String toAvocadoJsonString(List<DataRecord> avocadoRecords) {
        if (avocadoRecords == null || avocadoRecords.isEmpty()) {
            return "[]";
        }
        
        StringBuilder avocadoJson = new StringBuilder();
        avocadoJson.append("[\n");
        
        for (int i = 0; i < avocadoRecords.size(); i++) {
            DataRecord avocadoRecord = avocadoRecords.get(i);
            if (avocadoRecord instanceof AppleRecord) {
                AppleRecord avocadoAppleRecord = (AppleRecord) avocadoRecord;
                
                avocadoJson.append("  {\n");
                Set<String> avocadoFieldNames = avocadoAppleRecord.getFieldNames();
                int avocadoFieldIndex = 0;
                
                for (String avocadoFieldName : avocadoFieldNames) {
                    Object avocadoValue = avocadoAppleRecord.getField(avocadoFieldName);
                    avocadoJson.append("    \"").append(avocadoFieldName).append("\": ");
                    
                    if (avocadoValue instanceof String) {
                        avocadoJson.append("\"").append(avocadoValue).append("\"");
                    } else {
                        avocadoJson.append(avocadoValue);
                    }
                    
                    if (avocadoFieldIndex < avocadoFieldNames.size() - 1) {
                        avocadoJson.append(",");
                    }
                    avocadoJson.append("\n");
                    avocadoFieldIndex++;
                }
                
                avocadoJson.append("  }");
                if (i < avocadoRecords.size() - 1) {
                    avocadoJson.append(",");
                }
                avocadoJson.append("\n");
            }
        }
        
        avocadoJson.append("]");
        return avocadoJson.toString();
    }
}
