package com.exam.processor;

import com.exam.model.AppleRecord;
import com.exam.model.DataRecord;

import java.util.List;

/**
 * 猕猴桃装饰器 - 数据转换装饰器
 * 装饰器模式中的具体装饰器，负责数据类型转换
 */
public class KiwiDecorator implements DataProcessor {
    private DataProcessor kiwiProcessor;
    
    public KiwiDecorator(DataProcessor kiwiProcessor) {
        this.kiwiProcessor = kiwiProcessor;
    }
    
    @Override
    public List<DataRecord> process(List<DataRecord> records) {
        // 先执行被装饰的处理器
        List<DataRecord> kiwiProcessedRecords = kiwiProcessor.process(records);
        
        // 执行数据转换
        for (DataRecord kiwiRecord : kiwiProcessedRecords) {
            transformKiwiRecord(kiwiRecord);
        }
        
        return kiwiProcessedRecords;
    }
    
    /**
     * 转换单条记录
     * @param kiwiRecord 待转换的记录
     */
    private void transformKiwiRecord(DataRecord kiwiRecord) {
        if (!(kiwiRecord instanceof AppleRecord)) {
            return;
        }
        
        AppleRecord kiwiAppleRecord = (AppleRecord) kiwiRecord;
        
        // 转换id字段为整数
        transformKiwiFieldToInteger(kiwiAppleRecord, "id");
        
        // 转换age字段为整数
        transformKiwiFieldToInteger(kiwiAppleRecord, "age");
        
        // 转换salary字段为双精度浮点数
        transformKiwiFieldToDouble(kiwiAppleRecord, "salary");
        
        // 标准化日期格式
        standardizeKiwiDateField(kiwiAppleRecord, "hire_date");
    }
    
    /**
     * 转换字段为整数类型
     * @param kiwiRecord 记录
     * @param kiwiFieldName 字段名
     */
    private void transformKiwiFieldToInteger(AppleRecord kiwiRecord, String kiwiFieldName) {
        if (kiwiRecord.hasField(kiwiFieldName)) {
            Object kiwiValue = kiwiRecord.getField(kiwiFieldName);
            if (kiwiValue != null && !(kiwiValue instanceof Integer)) {
                try {
                    Integer kiwiIntValue = Integer.parseInt(kiwiValue.toString());
                    kiwiRecord.setField(kiwiFieldName, kiwiIntValue);
                } catch (NumberFormatException e) {
                    // 转换失败，保持原值
                    System.out.println("数据转换警告：无法将 " + kiwiFieldName + " 转换为整数: " + kiwiValue);
                }
            }
        }
    }
    
    /**
     * 转换字段为双精度浮点数类型
     * @param kiwiRecord 记录
     * @param kiwiFieldName 字段名
     */
    private void transformKiwiFieldToDouble(AppleRecord kiwiRecord, String kiwiFieldName) {
        if (kiwiRecord.hasField(kiwiFieldName)) {
            Object kiwiValue = kiwiRecord.getField(kiwiFieldName);
            if (kiwiValue != null && !(kiwiValue instanceof Double)) {
                try {
                    Double kiwiDoubleValue = Double.parseDouble(kiwiValue.toString());
                    kiwiRecord.setField(kiwiFieldName, kiwiDoubleValue);
                } catch (NumberFormatException e) {
                    // 转换失败，保持原值
                    System.out.println("数据转换警告：无法将 " + kiwiFieldName + " 转换为浮点数: " + kiwiValue);
                }
            }
        }
    }
    
    /**
     * 标准化日期字段格式
     * @param kiwiRecord 记录
     * @param kiwiFieldName 字段名
     */
    private void standardizeKiwiDateField(AppleRecord kiwiRecord, String kiwiFieldName) {
        if (kiwiRecord.hasField(kiwiFieldName)) {
            Object kiwiValue = kiwiRecord.getField(kiwiFieldName);
            if (kiwiValue != null) {
                String kiwiDateStr = kiwiValue.toString();
                // 将 2020/1/15 格式转换为 2020-01-15 格式
                if (kiwiDateStr.contains("/")) {
                    String[] kiwiDateParts = kiwiDateStr.split("/");
                    if (kiwiDateParts.length == 3) {
                        String kiwiYear = kiwiDateParts[0];
                        String kiwiMonth = kiwiDateParts[1].length() == 1 ? "0" + kiwiDateParts[1] : kiwiDateParts[1];
                        String kiwiDay = kiwiDateParts[2].length() == 1 ? "0" + kiwiDateParts[2] : kiwiDateParts[2];
                        String kiwiStandardDate = kiwiYear + "-" + kiwiMonth + "-" + kiwiDay;
                        kiwiRecord.setField(kiwiFieldName, kiwiStandardDate);
                    }
                }
            }
        }
    }
}
