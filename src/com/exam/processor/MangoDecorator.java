package com.exam.processor;

import com.exam.model.AppleRecord;
import com.exam.model.DataRecord;

import java.util.List;

/**
 * 芒果装饰器 - 数据清洗装饰器
 * 装饰器模式中的具体装饰器，负责数据清洗
 */
public class MangoDecorator implements DataProcessor {
    private DataProcessor mangoProcessor;
    
    public MangoDecorator(DataProcessor mangoProcessor) {
        this.mangoProcessor = mangoProcessor;
    }
    
    @Override
    public List<DataRecord> process(List<DataRecord> records) {
        // 先执行被装饰的处理器
        List<DataRecord> mangoProcessedRecords = mangoProcessor.process(records);
        
        // 执行数据清洗
        for (DataRecord mangoRecord : mangoProcessedRecords) {
            cleanMangoRecord(mangoRecord);
        }
        
        return mangoProcessedRecords;
    }
    
    /**
     * 清洗单条记录
     * @param mangoRecord 待清洗的记录
     */
    private void cleanMangoRecord(DataRecord mangoRecord) {
        if (!(mangoRecord instanceof AppleRecord)) {
            return;
        }
        
        AppleRecord mangoAppleRecord = (AppleRecord) mangoRecord;
        
        // 清洗字符串字段：去除前后空格
        for (String mangoFieldName : mangoAppleRecord.getFieldNames()) {
            Object mangoValue = mangoAppleRecord.getField(mangoFieldName);
            if (mangoValue instanceof String) {
                String mangoCleanValue = ((String) mangoValue).trim();
                mangoAppleRecord.setField(mangoFieldName, mangoCleanValue);
            }
        }
        
        // 处理缺失的age字段
        if (!mangoAppleRecord.hasField("age") || 
            mangoAppleRecord.getField("age") == null || 
            mangoAppleRecord.getFieldAsString("age").isEmpty()) {
            mangoAppleRecord.setField("age", "0"); // 默认年龄为0
            System.out.println("数据清洗：处理缺失的age字段，设置默认值0");
        }
        
        // 处理缺失的phone字段
        if (!mangoAppleRecord.hasField("phone") || 
            mangoAppleRecord.getField("phone") == null || 
            mangoAppleRecord.getFieldAsString("phone").isEmpty()) {
            mangoAppleRecord.setField("phone", "未提供"); // 默认电话
            System.out.println("数据清洗：处理缺失的phone字段，设置默认值'未提供'");
        }
        
        // 处理缺失的address字段
        if (!mangoAppleRecord.hasField("address") || 
            mangoAppleRecord.getField("address") == null || 
            mangoAppleRecord.getFieldAsString("address").isEmpty()) {
            mangoAppleRecord.setField("address", "未提供"); // 默认地址
            System.out.println("数据清洗：处理缺失的address字段，设置默认值'未提供'");
        }
        
        // 处理无效的age值（如XML中的"invalid_age"）
        String mangoAgeStr = mangoAppleRecord.getFieldAsString("age");
        if (mangoAgeStr != null && !mangoAgeStr.matches("\\d+")) {
            mangoAppleRecord.setField("age", "0");
            System.out.println("数据清洗：处理无效的age值 '" + mangoAgeStr + "'，设置为默认值0");
        }
    }
}
