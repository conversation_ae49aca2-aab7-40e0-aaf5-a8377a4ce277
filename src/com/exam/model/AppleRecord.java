package com.exam.model;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 苹果记录 - 数据记录的具体实现
 * 使用Map存储字段数据
 */
public class AppleRecord implements DataRecord {
    private Map<String, Object> appleFields;
    
    public AppleRecord() {
        this.appleFields = new HashMap<>();
    }
    
    public AppleRecord(Map<String, Object> appleFields) {
        this.appleFields = new HashMap<>(appleFields);
    }
    
    @Override
    public Object getField(String fieldName) {
        return appleFields.get(fieldName);
    }
    
    @Override
    public void setField(String fieldName, Object value) {
        appleFields.put(fieldName, value);
    }
    
    @Override
    public boolean hasField(String fieldName) {
        return appleFields.containsKey(fieldName);
    }
    
    @Override
    public Set<String> getFieldNames() {
        return appleFields.keySet();
    }
    
    /**
     * 获取字段值的字符串表示
     * @param fieldName 字段名
     * @return 字段值字符串
     */
    public String getFieldAsString(String fieldName) {
        Object value = getField(fieldName);
        return value != null ? value.toString() : "";
    }
    
    /**
     * 获取字段值的整数表示
     * @param fieldName 字段名
     * @return 字段值整数
     */
    public Integer getFieldAsInteger(String fieldName) {
        Object value = getField(fieldName);
        if (value == null) return null;
        if (value instanceof Integer) return (Integer) value;
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * 获取字段值的双精度浮点数表示
     * @param fieldName 字段名
     * @return 字段值双精度浮点数
     */
    public Double getFieldAsDouble(String fieldName) {
        Object value = getField(fieldName);
        if (value == null) return null;
        if (value instanceof Double) return (Double) value;
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("AppleRecord{");
        for (Map.Entry<String, Object> entry : appleFields.entrySet()) {
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append(", ");
        }
        if (sb.length() > 12) {
            sb.setLength(sb.length() - 2); // 移除最后的", "
        }
        sb.append("}");
        return sb.toString();
    }
}
