package com.exam.parser;

/**
 * 解析器工厂
 * 根据文件类型创建对应的解析器
 */
public class FileParserFactory {
    /**
     * 创建文件解析器
     * @param fileType 文件类型（csv, json, xml）
     * @return 对应的文件解析器
     */
    public static FileParser createParser(String fileType) {
        if (fileType == null) {
            throw new IllegalArgumentException("文件类型不能为空");
        }

        switch (fileType.toLowerCase()) {
            case "csv":
                return new BananaParser();
            case "json":
                return new OrangeParser();
            case "xml":
                return new GrapeParser();
            default:
                throw new IllegalArgumentException("不支持的文件类型: " + fileType);
        }
    }
}
