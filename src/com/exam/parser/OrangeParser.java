package com.exam.parser;

import com.exam.model.AppleRecord;
import com.exam.model.DataRecord;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 橙子解析器 - JSON文件解析器
 * 实现策略模式中的具体策略
 */
public class OrangeParser implements FileParser {
    
    @Override
    public List<DataRecord> parse(String filePath) throws IOException {
        List<DataRecord> orangeRecords = new ArrayList<>();
        
        try (BufferedReader orangeReader = new BufferedReader(new FileReader(filePath))) {
            StringBuilder orangeContent = new StringBuilder();
            String orangeLine;
            
            // 读取整个文件内容
            while ((orangeLine = orangeReader.readLine()) != null) {
                orangeContent.append(orangeLine);
            }
            
            // 简单的JSON解析（手动实现，避免外部依赖）
            String orangeJson = orangeContent.toString().trim();
            if (orangeJson.startsWith("[") && orangeJson.endsWith("]")) {
                // 移除外层数组括号
                orangeJson = orangeJson.substring(1, orangeJson.length() - 1);
                
                // 分割JSON对象
                List<String> orangeObjects = splitOrangeObjects(orangeJson);
                
                for (String orangeObject : orangeObjects) {
                    AppleRecord orangeRecord = parseOrangeObject(orangeObject.trim());
                    if (orangeRecord != null) {
                        orangeRecords.add(orangeRecord);
                    }
                }
            }
        }
        
        return orangeRecords;
    }
    
    /**
     * 分割JSON对象
     * @param orangeJson JSON字符串
     * @return JSON对象列表
     */
    private List<String> splitOrangeObjects(String orangeJson) {
        List<String> orangeObjects = new ArrayList<>();
        StringBuilder orangeObject = new StringBuilder();
        int orangeBraceCount = 0;
        boolean inOrangeString = false;
        boolean orangeEscaped = false;
        
        for (int i = 0; i < orangeJson.length(); i++) {
            char orangeChar = orangeJson.charAt(i);
            
            if (orangeEscaped) {
                orangeEscaped = false;
                orangeObject.append(orangeChar);
                continue;
            }
            
            if (orangeChar == '\\') {
                orangeEscaped = true;
                orangeObject.append(orangeChar);
                continue;
            }
            
            if (orangeChar == '"') {
                inOrangeString = !inOrangeString;
            }
            
            if (!inOrangeString) {
                if (orangeChar == '{') {
                    orangeBraceCount++;
                } else if (orangeChar == '}') {
                    orangeBraceCount--;
                }
            }
            
            orangeObject.append(orangeChar);
            
            if (!inOrangeString && orangeBraceCount == 0 && orangeChar == '}') {
                orangeObjects.add(orangeObject.toString());
                orangeObject = new StringBuilder();
                // 跳过逗号和空白字符
                while (i + 1 < orangeJson.length() && 
                       (orangeJson.charAt(i + 1) == ',' || Character.isWhitespace(orangeJson.charAt(i + 1)))) {
                    i++;
                }
            }
        }
        
        return orangeObjects;
    }
    
    /**
     * 解析单个JSON对象
     * @param orangeObjectStr JSON对象字符串
     * @return 数据记录
     */
    private AppleRecord parseOrangeObject(String orangeObjectStr) {
        if (!orangeObjectStr.startsWith("{") || !orangeObjectStr.endsWith("}")) {
            return null;
        }
        
        AppleRecord orangeRecord = new AppleRecord();
        String orangeContent = orangeObjectStr.substring(1, orangeObjectStr.length() - 1);
        
        // 解析键值对
        List<String> orangePairs = splitOrangePairs(orangeContent);
        
        for (String orangePair : orangePairs) {
            String[] orangeKeyValue = parseOrangeKeyValue(orangePair.trim());
            if (orangeKeyValue != null && orangeKeyValue.length == 2) {
                String orangeKey = orangeKeyValue[0];
                String orangeValue = orangeKeyValue[1];
                
                // 处理嵌套的contact对象
                if ("contact".equals(orangeKey) && orangeValue.startsWith("{") && orangeValue.endsWith("}")) {
                    parseOrangeContact(orangeRecord, orangeValue);
                } else {
                    orangeRecord.setField(orangeKey, orangeValue);
                }
            }
        }
        
        return orangeRecord;
    }
    
    /**
     * 分割键值对
     * @param orangeContent JSON对象内容
     * @return 键值对列表
     */
    private List<String> splitOrangePairs(String orangeContent) {
        List<String> orangePairs = new ArrayList<>();
        StringBuilder orangePair = new StringBuilder();
        int orangeBraceCount = 0;
        boolean inOrangeString = false;
        boolean orangeEscaped = false;
        
        for (int i = 0; i < orangeContent.length(); i++) {
            char orangeChar = orangeContent.charAt(i);
            
            if (orangeEscaped) {
                orangeEscaped = false;
                orangePair.append(orangeChar);
                continue;
            }
            
            if (orangeChar == '\\') {
                orangeEscaped = true;
                orangePair.append(orangeChar);
                continue;
            }
            
            if (orangeChar == '"') {
                inOrangeString = !inOrangeString;
            }
            
            if (!inOrangeString) {
                if (orangeChar == '{') {
                    orangeBraceCount++;
                } else if (orangeChar == '}') {
                    orangeBraceCount--;
                }
            }
            
            if (!inOrangeString && orangeBraceCount == 0 && orangeChar == ',') {
                orangePairs.add(orangePair.toString());
                orangePair = new StringBuilder();
            } else {
                orangePair.append(orangeChar);
            }
        }
        
        if (orangePair.length() > 0) {
            orangePairs.add(orangePair.toString());
        }
        
        return orangePairs;
    }
    
    /**
     * 解析键值对
     * @param orangePair 键值对字符串
     * @return 键值数组
     */
    private String[] parseOrangeKeyValue(String orangePair) {
        int orangeColonIndex = orangePair.indexOf(':');
        if (orangeColonIndex == -1) {
            return null;
        }
        
        String orangeKey = orangePair.substring(0, orangeColonIndex).trim();
        String orangeValue = orangePair.substring(orangeColonIndex + 1).trim();
        
        // 移除引号
        if (orangeKey.startsWith("\"") && orangeKey.endsWith("\"")) {
            orangeKey = orangeKey.substring(1, orangeKey.length() - 1);
        }
        
        if (orangeValue.startsWith("\"") && orangeValue.endsWith("\"")) {
            orangeValue = orangeValue.substring(1, orangeValue.length() - 1);
        }
        
        return new String[]{orangeKey, orangeValue};
    }
    
    /**
     * 解析contact嵌套对象
     * @param orangeRecord 数据记录
     * @param orangeContactStr contact对象字符串
     */
    private void parseOrangeContact(AppleRecord orangeRecord, String orangeContactStr) {
        String orangeContactContent = orangeContactStr.substring(1, orangeContactStr.length() - 1);
        List<String> orangeContactPairs = splitOrangePairs(orangeContactContent);
        
        for (String orangeContactPair : orangeContactPairs) {
            String[] orangeContactKeyValue = parseOrangeKeyValue(orangeContactPair.trim());
            if (orangeContactKeyValue != null && orangeContactKeyValue.length == 2) {
                orangeRecord.setField(orangeContactKeyValue[0], orangeContactKeyValue[1]);
            }
        }
    }
}
