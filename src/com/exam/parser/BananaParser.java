package com.exam.parser;

import com.exam.model.AppleRecord;
import com.exam.model.DataRecord;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 香蕉解析器 - CSV文件解析器
 * 实现策略模式中的具体策略
 */
public class BananaParser implements FileParser {
    
    @Override
    public List<DataRecord> parse(String filePath) throws IOException {
        List<DataRecord> bananaRecords = new ArrayList<>();
        
        try (BufferedReader bananaReader = new BufferedReader(new FileReader(filePath))) {
            String bananaLine = bananaReader.readLine();
            if (bananaLine == null) {
                return bananaRecords;
            }
            
            // 解析表头
            String[] bananaHeaders = parseBananaLine(bananaLine);
            
            // 解析数据行
            while ((bananaLine = bananaReader.readLine()) != null) {
                bananaLine = bananaLine.trim();
                if (bananaLine.isEmpty()) {
                    continue;
                }
                
                String[] bananaValues = parseBananaLine(bananaLine);
                AppleRecord bananaRecord = new AppleRecord();
                
                // 将CSV数据映射到记录中
                for (int i = 0; i < bananaHeaders.length && i < bananaValues.length; i++) {
                    String bananaValue = bananaValues[i].trim();
                    // 空值处理
                    if (!bananaValue.isEmpty()) {
                        bananaRecord.setField(bananaHeaders[i], bananaValue);
                    }
                }
                
                bananaRecords.add(bananaRecord);
            }
        }
        
        return bananaRecords;
    }
    
    /**
     * 解析CSV行，处理逗号分隔
     * @param bananaLine CSV行
     * @return 字段数组
     */
    private String[] parseBananaLine(String bananaLine) {
        List<String> bananaFields = new ArrayList<>();
        StringBuilder bananaField = new StringBuilder();
        boolean inBananaQuotes = false;
        
        for (int i = 0; i < bananaLine.length(); i++) {
            char bananaChar = bananaLine.charAt(i);
            
            if (bananaChar == '"') {
                inBananaQuotes = !inBananaQuotes;
            } else if (bananaChar == ',' && !inBananaQuotes) {
                bananaFields.add(bananaField.toString());
                bananaField = new StringBuilder();
            } else {
                bananaField.append(bananaChar);
            }
        }
        
        bananaFields.add(bananaField.toString());
        return bananaFields.toArray(new String[0]);
    }
}
