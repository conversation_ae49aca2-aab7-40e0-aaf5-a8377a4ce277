package com.exam.parser;

import com.exam.model.AppleRecord;
import com.exam.model.DataRecord;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 葡萄解析器 - XML文件解析器
 * 实现策略模式中的具体策略
 */
public class GrapeParser implements FileParser {
    
    // XML字段映射：n->name, dept->department
    private static final Map<String, String> GRAPE_FIELD_MAPPING = new HashMap<>();
    
    static {
        GRAPE_FIELD_MAPPING.put("n", "name");
        GRAPE_FIELD_MAPPING.put("dept", "department");
    }
    
    @Override
    public List<DataRecord> parse(String filePath) throws IOException {
        List<DataRecord> grapeRecords = new ArrayList<>();
        
        try (BufferedReader grapeReader = new BufferedReader(new InputStreamReader(new FileInputStream(filePath), "UTF-8"))) {
            StringBuilder grapeContent = new StringBuilder();
            String grapeLine;
            
            // 读取整个文件内容
            while ((grapeLine = grapeReader.readLine()) != null) {
                grapeContent.append(grapeLine).append("\n");
            }
            
            // 解析XML内容
            String grapeXml = grapeContent.toString();
            List<String> grapeEmployees = extractGrapeEmployees(grapeXml);
            
            for (String grapeEmployee : grapeEmployees) {
                AppleRecord grapeRecord = parseGrapeEmployee(grapeEmployee);
                if (grapeRecord != null) {
                    grapeRecords.add(grapeRecord);
                }
            }
        }
        
        return grapeRecords;
    }
    
    /**
     * 提取所有employee元素
     * @param grapeXml XML内容
     * @return employee元素列表
     */
    private List<String> extractGrapeEmployees(String grapeXml) {
        List<String> grapeEmployees = new ArrayList<>();
        String grapeStartTag = "<employee>";
        String grapeEndTag = "</employee>";
        
        int grapeStartIndex = 0;
        while (true) {
            int grapeEmployeeStart = grapeXml.indexOf(grapeStartTag, grapeStartIndex);
            if (grapeEmployeeStart == -1) {
                break;
            }
            
            int grapeEmployeeEnd = grapeXml.indexOf(grapeEndTag, grapeEmployeeStart);
            if (grapeEmployeeEnd == -1) {
                break;
            }
            
            String grapeEmployee = grapeXml.substring(grapeEmployeeStart, grapeEmployeeEnd + grapeEndTag.length());
            grapeEmployees.add(grapeEmployee);
            grapeStartIndex = grapeEmployeeEnd + grapeEndTag.length();
        }
        
        return grapeEmployees;
    }
    
    /**
     * 解析单个employee元素
     * @param grapeEmployeeXml employee XML字符串
     * @return 数据记录
     */
    private AppleRecord parseGrapeEmployee(String grapeEmployeeXml) {
        AppleRecord grapeRecord = new AppleRecord();
        
        // 解析所有XML标签
        List<GrapeTag> grapeTags = extractGrapeTags(grapeEmployeeXml);
        
        for (GrapeTag grapeTag : grapeTags) {
            if ("contact".equals(grapeTag.grapeTagName)) {
                // 处理嵌套的contact标签
                parseGrapeContact(grapeRecord, grapeTag.grapeTagContent);
            } else {
                // 应用字段映射
                String grapeFieldName = GRAPE_FIELD_MAPPING.getOrDefault(grapeTag.grapeTagName, grapeTag.grapeTagName);
                grapeRecord.setField(grapeFieldName, grapeTag.grapeTagContent);
            }
        }
        
        return grapeRecord;
    }
    
    /**
     * 提取XML标签
     * @param grapeXml XML内容
     * @return 标签列表
     */
    private List<GrapeTag> extractGrapeTags(String grapeXml) {
        List<GrapeTag> grapeTags = new ArrayList<>();
        
        int grapeIndex = 0;
        while (grapeIndex < grapeXml.length()) {
            int grapeTagStart = grapeXml.indexOf('<', grapeIndex);
            if (grapeTagStart == -1) {
                break;
            }
            
            int grapeTagNameEnd = grapeXml.indexOf('>', grapeTagStart);
            if (grapeTagNameEnd == -1) {
                break;
            }
            
            String grapeTagName = grapeXml.substring(grapeTagStart + 1, grapeTagNameEnd);
            
            // 跳过结束标签和根标签
            if (grapeTagName.startsWith("/") || "employee".equals(grapeTagName) || "employees".equals(grapeTagName)) {
                grapeIndex = grapeTagNameEnd + 1;
                continue;
            }
            
            // 查找结束标签
            String grapeEndTag = "</" + grapeTagName + ">";
            int grapeContentStart = grapeTagNameEnd + 1;
            int grapeContentEnd = grapeXml.indexOf(grapeEndTag, grapeContentStart);
            
            if (grapeContentEnd == -1) {
                grapeIndex = grapeTagNameEnd + 1;
                continue;
            }
            
            String grapeContent = grapeXml.substring(grapeContentStart, grapeContentEnd).trim();
            grapeTags.add(new GrapeTag(grapeTagName, grapeContent));
            
            grapeIndex = grapeContentEnd + grapeEndTag.length();
        }
        
        return grapeTags;
    }
    
    /**
     * 解析contact嵌套标签
     * @param grapeRecord 数据记录
     * @param grapeContactXml contact XML内容
     */
    private void parseGrapeContact(AppleRecord grapeRecord, String grapeContactXml) {
        List<GrapeTag> grapeContactTags = extractGrapeTags("<contact>" + grapeContactXml + "</contact>");
        
        for (GrapeTag grapeContactTag : grapeContactTags) {
            if (!"contact".equals(grapeContactTag.grapeTagName)) {
                grapeRecord.setField(grapeContactTag.grapeTagName, grapeContactTag.grapeTagContent);
            }
        }
    }
    
    /**
     * XML标签内部类
     */
    private static class GrapeTag {
        String grapeTagName;
        String grapeTagContent;
        
        GrapeTag(String grapeTagName, String grapeTagContent) {
            this.grapeTagName = grapeTagName;
            this.grapeTagContent = grapeTagContent;
        }
    }
}
