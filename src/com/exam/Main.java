package com.exam;

import com.exam.model.DataRecord;
import com.exam.parser.FileParser;
import com.exam.parser.FileParserFactory;
import com.exam.processor.DataProcessor;
import com.exam.processor.KiwiDecorator;
import com.exam.processor.MangoDecorator;
import com.exam.processor.PeachProcessor;
import com.exam.report.CoconutReport;
import com.exam.service.WatermelonService;
import com.exam.util.AvocadoUtils;
import com.exam.validator.CherryRule;
import com.exam.validator.LemonRule;
import com.exam.validator.PineappleValidator;
import com.exam.validator.StrawberryRule;
import com.exam.validator.ValidationRule;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 主类
 * 包含测试用例和主方法
 */
public class Main {
    // 测试数据文件路径
    private static final String TEST_DATA_DIR = "test_data";
    private static final String CSV_FILE = TEST_DATA_DIR + "/employees.csv";
    private static final String JSON_FILE = TEST_DATA_DIR + "/employees.json";
    private static final String XML_FILE = TEST_DATA_DIR + "/employees.xml";
    
    public static void main(String[] args) {
        
        // 创建验证规则
        Map<String, ValidationRule> validationRules = createValidationRules();
        
        System.out.println("=== 数据湖平台文件处理系统 ===\n");
        
        // 测试用例1：CSV文件处理
        System.out.println("测试用例1：CSV文件处理");
        processFile("csv", CSV_FILE, validationRules);
        
        System.out.println("\n");
        
        // 测试用例2：JSON文件处理
        System.out.println("测试用例2：JSON文件处理");
        processFile("json", JSON_FILE, validationRules);
        
        System.out.println("\n");
        
        // 测试用例3：XML文件处理
        System.out.println("测试用例3：XML文件处理");
        processFile("xml", XML_FILE, validationRules);
    }
    
    /**
     * 处理文件
     * @param fileType 文件类型
     * @param filePath 文件路径
     * @param validationRules 验证规则
     */
    private static void processFile(String fileType, String filePath, Map<String, ValidationRule> validationRules) {
        try {
            // 创建解析器
            FileParser parser = FileParserFactory.createParser(fileType);

            // 创建处理器链（装饰器模式）
            DataProcessor processor = new PeachProcessor(); // 基础处理器
            processor = new MangoDecorator(processor); // 添加数据清洗装饰器
            processor = new KiwiDecorator(processor); // 添加数据转换装饰器

            // 创建验证器
            PineappleValidator validator = new PineappleValidator(validationRules);

            // 创建文件处理服务
            WatermelonService service = new WatermelonService(parser, processor, validator);

            // 处理文件
            CoconutReport report = service.processWatermelonFile(filePath);

            // 输出处理报告
            report.printCoconutReport();

            // 输出处理后的数据
            List<DataRecord> validRecords = service.processWatermelonFileAndGetRecords(filePath);
            AvocadoUtils.outputAvocadoSimpleRecords(validRecords);

        } catch (Exception e) {
            System.err.println("处理文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
   
    
    /**
     * 创建验证规则
     * @return 验证规则映射
     */
    private static Map<String, ValidationRule> createValidationRules() {
        Map<String, ValidationRule> rules = new HashMap<>();

        // 必填字段验证
        rules.put("name", new LemonRule("姓名不能为空"));
        rules.put("id", new LemonRule("ID不能为空"));

        // 数据类型验证
        rules.put("age", new CherryRule(Integer.class, "年龄必须是数字"));
        rules.put("salary", new CherryRule(Double.class, "薪资必须是数字"));

        // 取值范围验证
        rules.put("age", new StrawberryRule(0, 120, "年龄必须在0-120之间"));
        rules.put("salary", new StrawberryRule(0.0, 1000000.0, "薪资必须在0-1000000之间"));

        return rules;
    }
    
    
}
