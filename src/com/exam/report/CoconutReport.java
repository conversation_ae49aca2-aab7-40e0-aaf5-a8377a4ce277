package com.exam.report;

/**
 * 椰子报告 - 性能统计报告
 * 记录文件处理的时间和处理的记录数
 */
public class CoconutReport {
    private long coconutStartTime;
    private long coconutEndTime;
    private int coconutTotalRecords;
    private int coconutValidRecords;
    private int coconutInvalidRecords;
    private String coconutFileName;
    
    public CoconutReport(String coconutFileName) {
        this.coconutFileName = coconutFileName;
        this.coconutStartTime = System.currentTimeMillis();
        this.coconutTotalRecords = 0;
        this.coconutValidRecords = 0;
        this.coconutInvalidRecords = 0;
    }
    
    /**
     * 开始计时
     */
    public void startCoconutTiming() {
        this.coconutStartTime = System.currentTimeMillis();
    }
    
    /**
     * 结束计时
     */
    public void endCoconutTiming() {
        this.coconutEndTime = System.currentTimeMillis();
    }
    
    /**
     * 设置总记录数
     * @param coconutTotalRecords 总记录数
     */
    public void setCoconutTotalRecords(int coconutTotalRecords) {
        this.coconutTotalRecords = coconutTotalRecords;
    }
    
    /**
     * 设置有效记录数
     * @param coconutValidRecords 有效记录数
     */
    public void setCoconutValidRecords(int coconutValidRecords) {
        this.coconutValidRecords = coconutValidRecords;
        this.coconutInvalidRecords = this.coconutTotalRecords - coconutValidRecords;
    }
    
    /**
     * 获取处理时间（毫秒）
     * @return 处理时间
     */
    public long getCoconutProcessingTime() {
        return coconutEndTime - coconutStartTime;
    }
    
    /**
     * 打印报告
     */
    public void printCoconutReport() {
        System.out.println("=== 处理报告 ===");
        System.out.println("文件名: " + coconutFileName);
        System.out.println("处理时间: " + getCoconutProcessingTime() + " 毫秒");
        System.out.println("总记录数: " + coconutTotalRecords);
        System.out.println("有效记录数: " + coconutValidRecords);
        System.out.println("无效记录数: " + coconutInvalidRecords);
        if (coconutTotalRecords > 0) {
            double coconutSuccessRate = (double) coconutValidRecords / coconutTotalRecords * 100;
            System.out.println("成功率: " + String.format("%.2f", coconutSuccessRate) + "%");
        }
        System.out.println("================");
    }
    
    // Getter方法
    public String getCoconutFileName() {
        return coconutFileName;
    }
    
    public int getCoconutTotalRecords() {
        return coconutTotalRecords;
    }
    
    public int getCoconutValidRecords() {
        return coconutValidRecords;
    }
    
    public int getCoconutInvalidRecords() {
        return coconutInvalidRecords;
    }
}
